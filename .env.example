OPENAI_ENDPOINT=https://ark.cn-beijing.volces.com/api/v3
OPENAI_API_KEY=ebe26886-1e4a-4467-9f8d-ce5b4707628a

ANTHROPIC_API_KEY=
ANTHROPIC_ENDPOINT=https://api.anthropic.com

GOOGLE_API_KEY=

AZURE_OPENAI_ENDPOINT=
AZURE_OPENAI_API_KEY=
AZURE_OPENAI_API_VERSION=2025-01-01-preview

DEEPSEEK_ENDPOINT=https://ark.cn-beijing.volces.com/api/v3
DEEPSEEK_API_KEY="ebe26886-1e4a-4467-9f8d-ce5b4707628a"

MISTRAL_API_KEY=
MISTRAL_ENDPOINT=https://api.mistral.ai/v1

OLLAMA_ENDPOINT=http://localhost:11434

ALIBABA_ENDPOINT=https://dashscope.aliyuncs.com/compatible-mode/v1
ALIBABA_API_KEY=

MODELSCOPE_ENDPOINT=https://api-inference.modelscope.cn/v1
MODELSCOPE_API_KEY=

MOONSHOT_ENDPOINT=https://api.moonshot.cn/v1
MOONSHOT_API_KEY=

UNBOUND_ENDPOINT=https://api.getunbound.ai
UNBOUND_API_KEY=

SiliconFLOW_ENDPOINT=https://api.siliconflow.cn/v1/
SiliconFLOW_API_KEY=

IBM_ENDPOINT=https://us-south.ml.cloud.ibm.com
IBM_API_KEY=
IBM_PROJECT_ID=

GROK_ENDPOINT="https://api.x.ai/v1"
GROK_API_KEY=

#set default LLM
DEFAULT_LLM=deepseek


# Set to false to disable anonymized telemetry
ANONYMIZED_TELEMETRY=false

# LogLevel: Set to debug to enable verbose logging, set to result to get results only. Available: result | debug | info
BROWSER_USE_LOGGING_LEVEL=info

# Browser settings
BROWSER_PATH="C:\Program Files\Google\Chrome\Application\chrome.exe"
BROWSER_USER_DATA="C:\Users\<USER>\AppData\Local\google\Chrome\User Data"
BROWSER_DEBUGGING_PORT=9222
BROWSER_DEBUGGING_HOST=localhost
# Set to true to keep browser open between AI tasks
KEEP_BROWSER_OPEN=true
USE_OWN_BROWSER=false
BROWSER_CDP=
# Display settings
# Format: WIDTHxHEIGHTxDEPTH
RESOLUTION=1920x1080x24
# Width in pixels
RESOLUTION_WIDTH=1920
# Height in pixels
RESOLUTION_HEIGHT=1080

# VNC settings
VNC_PASSWORD=youvncpassword
