#!/usr/bin/env python3
"""
测试 DeepSeek + 禁用内存功能的修复
"""

import os
import sys
sys.path.append('src')

from src.utils.llm_provider import get_llm_model
from src.agent.browser_use.browser_use_agent import BrowserUseAgent

def test_deepseek_without_memory():
    """测试使用 DeepSeek 但禁用内存功能"""
    
    # 设置 DeepSeek API Key（如果有的话）
    deepseek_api_key = os.getenv("DEEPSEEK_API_KEY", "test-key")
    
    try:
        # 初始化 DeepSeek LLM
        print("正在初始化 DeepSeek LLM...")
        llm = get_llm_model(
            provider="deepseek",
            model_name="deepseek-chat",
            temperature=0.6,
            api_key=deepseek_api_key
        )
        print(f"LLM 初始化成功: {llm.__class__.__name__}")
        
        # 尝试创建 BrowserUseAgent，禁用内存功能
        print("正在创建 BrowserUseAgent（禁用内存）...")
        agent = BrowserUseAgent(
            task="测试任务",
            llm=llm,
            enable_memory=False  # 关键：禁用内存功能
        )
        print("BrowserUseAgent 创建成功！")
        print(f"内存功能状态: {agent.enable_memory}")
        print(f"内存对象: {agent.memory}")
        
        return True
        
    except Exception as e:
        print(f"错误: {e}")
        return False

if __name__ == "__main__":
    success = test_deepseek_without_memory()
    if success:
        print("\n✅ 修复成功！DeepSeek 可以在禁用内存功能的情况下正常工作。")
    else:
        print("\n❌ 修复失败，仍然存在问题。")
